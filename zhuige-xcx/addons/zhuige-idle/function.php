<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

if (!function_exists('zhuige_idle_shop_goods_format')) {
    /**
     * 格式化闲置物品数据
     */
    function zhuige_idle_shop_goods_format($post, $detail = false)
    {
        if (!$post) {
            return null;
        }
        
        $user_id = $post->post_author;
        $my_user_id = get_current_user_id();

        $user = [
            'user_id' => $user_id,
            'nickname' => get_user_meta($user_id, 'nickname', true),
            'avatar' => ZhuiGe_Xcx::user_avatar($user_id),
            'weixin' => get_user_meta($user_id, 'weixin', true)
        ];

        // 检查是否关注了作者
        if ($my_user_id) {
            global $wpdb;
            $table_follow_user = $wpdb->prefix . 'zhuige_xcx_follow_user';
            $follow_user_id_exist = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT id FROM `$table_follow_user` WHERE user_id=%d AND follow_user_id=%d",
                    $my_user_id,
                    $user_id
                )
            );
            $user['is_follow'] = ($follow_user_id_exist ? 1 : 0);
        } else {
            $user['is_follow'] = 0;
        }
        
        // 获取自定义字段
        $metabox_data = get_post_meta($post->ID, 'zhuige_idle_goods_options', true);
        $price = isset($metabox_data['price']) ? $metabox_data['price'] : '0';
        $stick = isset($metabox_data['stick']) ? $metabox_data['stick'] : false;

        // 解析图片数据
        $images = [];
        if (!empty($metabox_data['images'])) {
            if (is_string($metabox_data['images'])) {
                // 如果是JSON字符串，解析它
                $images_data = json_decode($metabox_data['images'], true);
                if (is_array($images_data)) {
                    foreach ($images_data as $img) {
                        if (isset($img['image']['url'])) {
                            $images[] = $img['image']['url'];
                        }
                    }
                }
            } elseif (is_array($metabox_data['images'])) {
                // 如果已经是数组
                foreach ($metabox_data['images'] as $img) {
                    if (isset($img['image']['url'])) {
                        $images[] = $img['image']['url'];
                    }
                }
            }
        }

        // 获取缩略图
        $thumb = '';
        if (!empty($images)) {
            $thumb = $images[0];
        } elseif (has_post_thumbnail($post->ID)) {
            $thumb = get_the_post_thumbnail_url($post->ID, 'medium');
        } else {
            $thumb = ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg';
        }
        
        // 获取分类信息
        $cat = ['id' => 0, 'name' => ''];
        $post_cats = get_the_terms($post->ID, 'idle_category');
        if ($post_cats && !is_wp_error($post_cats)) {
            $cat_data = $post_cats[0]; // 取第一个分类
            $cat = [
                'id' => $cat_data->term_id,
                'name' => $cat_data->name
            ];
        }

        // 获取标签
        $tags = [];
        $post_tags = get_the_terms($post->ID, 'post_tag');
        if ($post_tags && !is_wp_error($post_tags)) {
            foreach ($post_tags as $tag) {
                $tags[] = [
                    'id' => $tag->term_id,
                    'name' => $tag->name
                ];
            }
        }
        
        $data = [
            'id' => $post->ID,
            'title' => $post->post_title,
            'content' => $detail ? apply_filters('the_content', $post->post_content) : $post->post_excerpt,
            'excerpt' => $post->post_excerpt,
            'price' => $price,
            'thumb' => $thumb,
            'images' => $images,
            'stick' => $stick ? 1 : 0,
            'user' => $user,
            'author' => $user,
            'cat' => $cat,
            'rec_cat' => $cat,
            'tags' => $tags,
            'time' => zhuige_xcx_time_beautify($post->post_date_gmt),
            'comment_count' => $post->comment_count
        ];
        
        if ($detail) {
            // 详情页额外数据
            $author = $user;
            if (function_exists('zhuige_xcx_certify_is_certify')) {
                $author['certify'] = zhuige_xcx_certify_is_certify($user_id);
            }
            if (function_exists('zhuige_xcx_vip_is_vip')) {
                $author['vip'] = zhuige_xcx_vip_is_vip($user_id);
            }
            if (get_user_meta($user_id, 'weixin', true)) {
                $author['weixin'] = get_user_meta($user_id, 'weixin', true);
            }
            $data['author'] = $author;

            $data['is_like'] = 0;
            $data['is_favorite'] = 0;
            $data['is_comment'] = 0;
            $data['like_list'] = [];
            $data['favorites'] = 0;
            $data['comment_switch'] = true;
            $data['comment_require_mobile2'] = false;
            $data['comment_require_avatar2'] = false;
            $data['is_show_promotion'] = 0;
            $data['is_show_edit'] = 0;

            // 获取推荐商品
            $cat_id = $cat ? $cat['id'] : null;
            $data['recs'] = zhuige_idle_get_recommendations($post->ID, $cat_id);
            if (!empty($data['recs'])) {
                $data['rec_cat'] = $cat;
            }

            // 如果是当前用户的商品，显示编辑按钮
            if (is_user_logged_in() && get_current_user_id() == $user_id) {
                $data['is_show_edit'] = 1;
            }
        }
        
        return $data;
    }
}

if (!function_exists('zhuige_idle_get_recommendations')) {
    /**
     * 获取推荐商品
     */
    function zhuige_idle_get_recommendations($exclude_id, $cat_id, $limit = 4)
    {
        $args = [
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'post__not_in' => [$exclude_id],
            'orderby' => 'rand'
        ];
        
        if ($cat_id) {
            $args['tax_query'] = [
                [
                    'taxonomy' => 'idle_category',
                    'field' => 'term_id',
                    'terms' => $cat_id,
                ]
            ];
        }
        
        $posts = get_posts($args);
        $recommendations = [];
        
        foreach ($posts as $post) {
            $recommendations[] = zhuige_idle_shop_goods_format($post);
        }
        
        return $recommendations;
    }
}

if (!function_exists('zhuige_idle_get_categories')) {
    /**
     * 获取分类列表
     */
    function zhuige_idle_get_categories($with_count = false)
    {
        $args = [
            'taxonomy' => 'zhuige_idle_goods_cat',
            'hide_empty' => false,
            'orderby' => 'term_order',
            'order' => 'ASC'
        ];

        $terms = get_terms($args);
        $result = [];

        if (!is_wp_error($terms) && !empty($terms)) {
            foreach ($terms as $term) {
                $term_meta = get_term_meta($term->term_id, 'zhuige_idle_goods_cat_options', true);
                $image = '';
                if ($term_meta && isset($term_meta['image'])) {
                    $image = ZhuiGe_Xcx::option_image_url($term_meta['image']);
                }

                $cat_data = [
                    'id' => intval($term->term_id),
                    'name' => $term->name,
                    'description' => $term->description,
                    'image' => $image
                ];

                if ($with_count) {
                    $cat_data['count'] = intval($term->count);

                    // 获取该分类下的部分商品
                    $posts = get_posts([
                        'post_type' => 'zhuige_idle_goods',
                        'post_status' => 'publish',
                        'posts_per_page' => 6,
                        'tax_query' => [
                            [
                                'taxonomy' => 'zhuige_idle_goods_cat',
                                'field' => 'term_id',
                                'terms' => $term->term_id
                            ]
                        ]
                    ]);

                    $cat_data['list'] = [];
                    foreach ($posts as $post) {
                        $cat_data['list'][] = zhuige_idle_shop_goods_format($post);
                    }
                }

                $result[] = $cat_data;
            }
        }

        return $result;
    }
}

if (!function_exists('zhuige_idle_get_setting')) {
    /**
     * 获取闲置物品设置
     */
    function zhuige_idle_get_setting($key = null)
    {
        $settings = [
            'background' => ZhuiGe_Xcx::option_image_url(ZhuiGe_Xcx::option_value('idle_background'), ''),
            'slides' => zhuige_idle_format_slides(ZhuiGe_Xcx::option_value('idle_slides', [])),
            'icons' => zhuige_idle_format_icons(ZhuiGe_Xcx::option_value('idle_icons', [])),
            'nav_cats' => [],
            'list_ad' => zhuige_idle_format_list_ad(ZhuiGe_Xcx::option_value('idle_list_ad', [])),
            'bottom_menu' => zhuige_idle_format_bottom_menu(ZhuiGe_Xcx::option_value('idle_bottom_menu', [])),
            'share_img' => ZhuiGe_Xcx::option_image_url(ZhuiGe_Xcx::option_value('idle_share_img'), ''),
            'create_require_mobile' => ZhuiGe_Xcx::option_value('idle_create_require_mobile', 0),
            'create_require_avatar' => ZhuiGe_Xcx::option_value('idle_create_require_avatar', 0),
            'create_require_weixin' => ZhuiGe_Xcx::option_value('idle_create_require_weixin', 0),
            'fbxy' => ZhuiGe_Xcx::option_value('idle_create_licence') ? get_permalink(ZhuiGe_Xcx::option_value('idle_create_licence')) : ''
        ];

        if ($key) {
            return isset($settings[$key]) ? $settings[$key] : null;
        }

        return $settings;
    }
}

if (!function_exists('zhuige_idle_format_slides')) {
    /**
     * 格式化轮播图数据
     */
    function zhuige_idle_format_slides($slides)
    {
        $result = [];
        if (is_array($slides)) {
            foreach ($slides as $slide) {
                if (!empty($slide['switch']) && !empty($slide['image'])) {
                    $result[] = [
                        'title' => $slide['title'] ?? '',
                        'image' => ZhuiGe_Xcx::option_image_url($slide['image'], ''),
                        'link' => $slide['link'] ?? ''
                    ];
                }
            }
        }
        return $result;
    }
}

if (!function_exists('zhuige_idle_format_icons')) {
    /**
     * 格式化图标导航数据
     */
    function zhuige_idle_format_icons($icons)
    {
        $result = [];
        if (is_array($icons)) {
            foreach ($icons as $icon) {
                if (!empty($icon['switch']) && !empty($icon['image'])) {
                    $result[] = [
                        'title' => $icon['title'] ?? '',
                        'image' => ZhuiGe_Xcx::option_image_url($icon['image'], ''),
                        'link' => $icon['link'] ?? ''
                    ];
                }
            }
        }
        return $result;
    }
}

if (!function_exists('zhuige_idle_format_list_ad')) {
    /**
     * 格式化列表广告数据
     */
    function zhuige_idle_format_list_ad($list_ad)
    {
        $result = [];
        if (is_array($list_ad)) {
            foreach ($list_ad as $ad) {
                $items = [];
                if (!empty($ad['items']) && is_array($ad['items'])) {
                    foreach ($ad['items'] as $item) {
                        if (!empty($item['image'])) {
                            $items[] = [
                                'title' => $item['title'] ?? '',
                                'image' => ZhuiGe_Xcx::option_image_url($item['image'], ''),
                                'link' => $item['link'] ?? ''
                            ];
                        }
                    }
                }

                if (!empty($items)) {
                    $result[] = [
                        'first' => intval($ad['first'] ?? 3),
                        'frequency' => intval($ad['frequency'] ?? 10),
                        'items' => $items
                    ];
                }
            }
        }
        return $result;
    }
}

if (!function_exists('zhuige_idle_format_bottom_menu')) {
    /**
     * 格式化底部菜单数据
     */
    function zhuige_idle_format_bottom_menu($bottom_menu)
    {
        $result = [];
        if (is_array($bottom_menu)) {
            foreach ($bottom_menu as $menu) {
                if (!empty($menu['switch']) && !empty($menu['image'])) {
                    $result[] = [
                        'title' => $menu['title'] ?? '',
                        'image' => ZhuiGe_Xcx::option_image_url($menu['image'], ''),
                        'link' => $menu['link'] ?? ''
                    ];
                }
            }
        }
        return $result;
    }
}
